package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SCanBoQuanLy;
import com.fds.flex.hbs.dulieuchu.domain.SCapBacTrinhDo;
import com.fds.flex.hbs.dulieuchu.domain.SCapHoc;
import com.fds.flex.hbs.dulieuchu.domain.SChuongTrinhGiaoDuc;
import com.fds.flex.hbs.dulieuchu.domain.SHinhThucDaoTao;
import com.fds.flex.hbs.dulieuchu.domain.SKhoiNganhDaoTao;
import com.fds.flex.hbs.dulieuchu.domain.SLoaiVanBangChungChi;
import com.fds.flex.hbs.dulieuchu.domain.SXepLoaiVanBang;
import com.fds.flex.hbs.dulieuchu.domain.TCoQuanQuanLy;
import com.fds.flex.hbs.dulieuchu.domain.TCoSoGiaoDuc;
import com.fds.flex.hbs.dulieuchu.domain.TNguoiHoc;
import com.fds.flex.hbs.dulieuchu.domain.TVanBangChungChi;
import com.fds.flex.hbs.dulieuchu.service.dto.SCanBoQuanLyDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SCapBacTrinhDoDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SCapHocDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SChuongTrinhGiaoDucDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SHinhThucDaoTaoDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SKhoiNganhDaoTaoDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SLoaiVanBangChungChiDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SXepLoaiVanBangDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TCoQuanQuanLyDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TCoSoGiaoDucDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TNguoiHocDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TVanBangChungChiDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link TVanBangChungChi} and its DTO {@link TVanBangChungChiDTO}.
 */
@Mapper(componentModel = "spring")
public interface TVanBangChungChiMapper extends EntityMapper<TVanBangChungChiDTO, TVanBangChungChi> {
    @Mapping(target = "loaiVanBangChungChi", source = "loaiVanBangChungChi", qualifiedByName = "sLoaiVanBangChungChiId")
    @Mapping(target = "chuongTrinhGiaoDuc", source = "chuongTrinhGiaoDuc", qualifiedByName = "sChuongTrinhGiaoDucId")
    @Mapping(target = "capHoc", source = "capHoc", qualifiedByName = "sCapHocId")
    @Mapping(target = "khoiNganhDaoTao", source = "khoiNganhDaoTao", qualifiedByName = "sKhoiNganhDaoTaoId")
    @Mapping(target = "capBacTrinhDo", source = "capBacTrinhDo", qualifiedByName = "sCapBacTrinhDoId")
    @Mapping(target = "nguoiHoc", source = "nguoiHoc", qualifiedByName = "tNguoiHocId")
    @Mapping(target = "coSoGiaoDuc", source = "coSoGiaoDuc", qualifiedByName = "tCoSoGiaoDucId")
    @Mapping(target = "xepLoaiVanBang", source = "xepLoaiVanBang", qualifiedByName = "sXepLoaiVanBangId")
    @Mapping(target = "hinhThucDaoTao", source = "hinhThucDaoTao", qualifiedByName = "sHinhThucDaoTaoId")
    @Mapping(target = "nguoiKyVanBang", source = "nguoiKyVanBang", qualifiedByName = "sCanBoQuanLyId")
    @Mapping(target = "coQuanQuanLy", source = "coQuanQuanLy", qualifiedByName = "tCoQuanQuanLyId")
    TVanBangChungChiDTO toDto(TVanBangChungChi s);

    @InheritInverseConfiguration
    @Mapping(target = "loaiVanBangChungChi", ignore = true)
    @Mapping(target = "chuongTrinhGiaoDuc", ignore = true)
    @Mapping(target = "capHoc", ignore = true)
    @Mapping(target = "khoiNganhDaoTao", ignore = true)
    @Mapping(target = "capBacTrinhDo", ignore = true)
    @Mapping(target = "nguoiHoc", ignore = true)
    @Mapping(target = "coSoGiaoDuc", ignore = true)
    @Mapping(target = "xepLoaiVanBang", ignore = true)
    @Mapping(target = "hinhThucDaoTao", ignore = true)
    @Mapping(target = "nguoiKyVanBang", ignore = true)
    @Mapping(target = "coQuanQuanLy", ignore = true)
    TVanBangChungChi toEntity(TVanBangChungChiDTO tVanBangChungChiDTO);

    @InheritInverseConfiguration
    @Mapping(target = "loaiVanBangChungChi", ignore = true)
    @Mapping(target = "chuongTrinhGiaoDuc", ignore = true)
    @Mapping(target = "capHoc", ignore = true)
    @Mapping(target = "khoiNganhDaoTao", ignore = true)
    @Mapping(target = "capBacTrinhDo", ignore = true)
    @Mapping(target = "nguoiHoc", ignore = true)
    @Mapping(target = "coSoGiaoDuc", ignore = true)
    @Mapping(target = "xepLoaiVanBang", ignore = true)
    @Mapping(target = "hinhThucDaoTao", ignore = true)
    @Mapping(target = "nguoiKyVanBang", ignore = true)
    @Mapping(target = "coQuanQuanLy", ignore = true)
    void partialUpdate(@MappingTarget TVanBangChungChi entity, TVanBangChungChiDTO dto);


    @Named("sLoaiVanBangChungChiId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SLoaiVanBangChungChiDTO toDtoSLoaiVanBangChungChiId(SLoaiVanBangChungChi sLoaiVanBangChungChi);

    @Named("sChuongTrinhGiaoDucId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SChuongTrinhGiaoDucDTO toDtoSChuongTrinhGiaoDucId(SChuongTrinhGiaoDuc sChuongTrinhGiaoDuc);

    @Named("sCapHocId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SCapHocDTO toDtoSCapHocId(SCapHoc sCapHoc);

    @Named("sKhoiNganhDaoTaoId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SKhoiNganhDaoTaoDTO toDtoSKhoiNganhDaoTaoId(SKhoiNganhDaoTao sKhoiNganhDaoTao);

    @Named("sCapBacTrinhDoId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SCapBacTrinhDoDTO toDtoSCapBacTrinhDoId(SCapBacTrinhDo sCapBacTrinhDo);

    @Named("tNguoiHocId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TNguoiHocDTO toDtoTNguoiHocId(TNguoiHoc tNguoiHoc);

    @Named("tCoSoGiaoDucId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TCoSoGiaoDucDTO toDtoTCoSoGiaoDucId(TCoSoGiaoDuc tCoSoGiaoDuc);

    @Named("sXepLoaiVanBangId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SXepLoaiVanBangDTO toDtoSXepLoaiVanBangId(SXepLoaiVanBang sXepLoaiVanBang);

    @Named("sHinhThucDaoTaoId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SHinhThucDaoTaoDTO toDtoSHinhThucDaoTaoId(SHinhThucDaoTao sHinhThucDaoTao);

    @Named("sCanBoQuanLyId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SCanBoQuanLyDTO toDtoSCanBoQuanLyId(SCanBoQuanLy sCanBoQuanLy);

    @Named("tCoQuanQuanLyId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TCoQuanQuanLyDTO toDtoTCoQuanQuanLyId(TCoQuanQuanLy tCoQuanQuanLy);
}

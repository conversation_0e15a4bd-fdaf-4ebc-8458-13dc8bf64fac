package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SCapHanhChinh;
import com.fds.flex.hbs.dulieuchu.domain.SDiaChi;
import com.fds.flex.hbs.dulieuchu.domain.TCoQuanQuanLy;
import com.fds.flex.hbs.dulieuchu.service.dto.SCapHanhChinhDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SDiaChiDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TCoQuanQuanLyDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link TCoQuanQuanLy} and its DTO {@link TCoQuanQuanLyDTO}.
 */
@Mapper(componentModel = "spring")
public interface TCoQuanQuanLyMapper extends EntityMapper<TCoQuanQuanLyDTO, TCoQuanQuanLy> {
    @Mapping(target = "capHanhChinh", source = "capHanhChinh", qualifiedByName = "sCapHanhChinhId")
    @Mapping(target = "diaChi", source = "diaChi", qualifiedByName = "sDiaChiId")
    TCoQuanQuanLyDTO toDto(TCoQuanQuanLy s);

    @Named("sCapHanhChinhId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SCapHanhChinhDTO toDtoSCapHanhChinhId(SCapHanhChinh sCapHanhChinh);

    @Named("sDiaChiId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SDiaChiDTO toDtoSDiaChiId(SDiaChi sDiaChi);
}

package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SCapBacTrinhDo;
import com.fds.flex.hbs.dulieuchu.domain.SCapHoc;
import com.fds.flex.hbs.dulieuchu.domain.SChuongTrinhGiaoDuc;
import com.fds.flex.hbs.dulieuchu.domain.SHinhThucDaoTao;
import com.fds.flex.hbs.dulieuchu.domain.SHinhThucTuyenSinh;
import com.fds.flex.hbs.dulieuchu.domain.SKhoiNganhDaoTao;
import com.fds.flex.hbs.dulieuchu.domain.TCoSoGiaoDuc;
import com.fds.flex.hbs.dulieuchu.domain.TNguoiHoc;
import com.fds.flex.hbs.dulieuchu.domain.TQuaTrinhHocTap;
import com.fds.flex.hbs.dulieuchu.service.dto.SCapBacTrinhDoDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SCapHocDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SChuongTrinhGiaoDucDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SHinhThucDaoTaoDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SHinhThucTuyenSinhDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SKhoiNganhDaoTaoDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TCoSoGiaoDucDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TNguoiHocDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TQuaTrinhHocTapDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link TQuaTrinhHocTap} and its DTO {@link TQuaTrinhHocTapDTO}.
 */
@Mapper(componentModel = "spring")
public interface TQuaTrinhHocTapMapper extends EntityMapper<TQuaTrinhHocTapDTO, TQuaTrinhHocTap> {
    @Mapping(target = "nguoiHoc", source = "nguoiHoc", qualifiedByName = "tNguoiHocId")
    @Mapping(target = "coSoGiaoDuc", source = "coSoGiaoDuc", qualifiedByName = "tCoSoGiaoDucId")
    @Mapping(target = "hinhThucTuyenSinh", source = "hinhThucTuyenSinh", qualifiedByName = "sHinhThucTuyenSinhId")
    @Mapping(target = "chuongTrinhGiaoDuc", source = "chuongTrinhGiaoDuc", qualifiedByName = "sChuongTrinhGiaoDucId")
    @Mapping(target = "capHoc", source = "capHoc", qualifiedByName = "sCapHocId")
    @Mapping(target = "khoiNganhDaoTao", source = "khoiNganhDaoTao", qualifiedByName = "sKhoiNganhDaoTaoId")
    @Mapping(target = "capBacTrinhDo", source = "capBacTrinhDo", qualifiedByName = "sCapBacTrinhDoId")
    @Mapping(target = "hinhThucDaoTao", source = "hinhThucDaoTao", qualifiedByName = "sHinhThucDaoTaoId")
    TQuaTrinhHocTapDTO toDto(TQuaTrinhHocTap s);

    @InheritInverseConfiguration
    @Mapping(target = "nguoiHoc", ignore = true)
    @Mapping(target = "coSoGiaoDuc", ignore = true)
    @Mapping(target = "hinhThucTuyenSinh", ignore = true)
    @Mapping(target = "chuongTrinhGiaoDuc", ignore = true)
    @Mapping(target = "capHoc", ignore = true)
    @Mapping(target = "khoiNganhDaoTao", ignore = true)
    @Mapping(target = "capBacTrinhDo", ignore = true)
    @Mapping(target = "hinhThucDaoTao", ignore = true)
    TQuaTrinhHocTap toEntity(TQuaTrinhHocTapDTO tQuaTrinhHocTapDTO);

    @InheritInverseConfiguration
    @Mapping(target = "nguoiHoc", ignore = true)
    @Mapping(target = "coSoGiaoDuc", ignore = true)
    @Mapping(target = "hinhThucTuyenSinh", ignore = true)
    @Mapping(target = "chuongTrinhGiaoDuc", ignore = true)
    @Mapping(target = "capHoc", ignore = true)
    @Mapping(target = "khoiNganhDaoTao", ignore = true)
    @Mapping(target = "capBacTrinhDo", ignore = true)
    @Mapping(target = "hinhThucDaoTao", ignore = true)
    void partialUpdate(@MappingTarget TQuaTrinhHocTap entity, TQuaTrinhHocTapDTO dto);

    @Named("tNguoiHocId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TNguoiHocDTO toDtoTNguoiHocId(TNguoiHoc tNguoiHoc);

    @Named("tCoSoGiaoDucId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TCoSoGiaoDucDTO toDtoTCoSoGiaoDucId(TCoSoGiaoDuc tCoSoGiaoDuc);

    @Named("sHinhThucTuyenSinhId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SHinhThucTuyenSinhDTO toDtoSHinhThucTuyenSinhId(SHinhThucTuyenSinh sHinhThucTuyenSinh);

    @Named("sChuongTrinhGiaoDucId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SChuongTrinhGiaoDucDTO toDtoSChuongTrinhGiaoDucId(SChuongTrinhGiaoDuc sChuongTrinhGiaoDuc);

    @Named("sCapHocId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SCapHocDTO toDtoSCapHocId(SCapHoc sCapHoc);

    @Named("sKhoiNganhDaoTaoId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SKhoiNganhDaoTaoDTO toDtoSKhoiNganhDaoTaoId(SKhoiNganhDaoTao sKhoiNganhDaoTao);

    @Named("sCapBacTrinhDoId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SCapBacTrinhDoDTO toDtoSCapBacTrinhDoId(SCapBacTrinhDo sCapBacTrinhDo);

    @Named("sHinhThucDaoTaoId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SHinhThucDaoTaoDTO toDtoSHinhThucDaoTaoId(SHinhThucDaoTao sHinhThucDaoTao);
}

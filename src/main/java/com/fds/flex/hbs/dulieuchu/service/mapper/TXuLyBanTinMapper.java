package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SDacTaBanTin;
import com.fds.flex.hbs.dulieuchu.domain.SKieuNenDuLieu;
import com.fds.flex.hbs.dulieuchu.domain.SLoiXuLyBanTin;
import com.fds.flex.hbs.dulieuchu.domain.TTepDuLieu;
import com.fds.flex.hbs.dulieuchu.domain.TXuLyBanTin;
import com.fds.flex.hbs.dulieuchu.service.dto.SDacTaBanTinDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SKieuNenDuLieuDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SLoiXuLyBanTinDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TTepDuLieuDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TXuLyBanTinDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link TXuLyBanTin} and its DTO {@link TXuLyBanTinDTO}.
 */
@Mapper(componentModel = "spring")
public interface TXuLyBanTinMapper extends EntityMapper<TXuLyBanTinDTO, TXuLyBanTin> {
    @Mapping(target = "banTinYeuCau", source = "banTinYeuCau", qualifiedByName = "sDacTaBanTinId")
    @Mapping(target = "banTinTraLoi", source = "banTinTraLoi", qualifiedByName = "sDacTaBanTinId")
    @Mapping(target = "kieuNenDuLieu", source = "kieuNenDuLieu", qualifiedByName = "sKieuNenDuLieuId")
    @Mapping(target = "loiXuLyBanTinId", source = "loiXuLyBanTinId", qualifiedByName = "sLoiXuLyBanTinId")
    @Mapping(target = "duLieuBanTinYeuCau", source = "duLieuBanTinYeuCau", qualifiedByName = "tTepDuLieuId")
    @Mapping(target = "duLieuBanTinTraLoi", source = "duLieuBanTinTraLoi", qualifiedByName = "tTepDuLieuId")
    TXuLyBanTinDTO toDto(TXuLyBanTin s);

    @InheritInverseConfiguration
    @Mapping(target = "banTinYeuCau", ignore = true)
    @Mapping(target = "banTinTraLoi", ignore = true)
    @Mapping(target = "kieuNenDuLieu", ignore = true)
    @Mapping(target = "loiXuLyBanTinId", ignore = true)
    @Mapping(target = "duLieuBanTinYeuCau", ignore = true)
    @Mapping(target = "duLieuBanTinTraLoi", ignore = true)
    TXuLyBanTin toEntity(TXuLyBanTinDTO tXuLyBanTinDTO);

    @InheritInverseConfiguration
    @Mapping(target = "banTinYeuCau", ignore = true)
    @Mapping(target = "banTinTraLoi", ignore = true)
    @Mapping(target = "kieuNenDuLieu", ignore = true)
    @Mapping(target = "loiXuLyBanTinId", ignore = true)
    @Mapping(target = "duLieuBanTinYeuCau", ignore = true)
    @Mapping(target = "duLieuBanTinTraLoi", ignore = true)
    void partialUpdate(@MappingTarget TXuLyBanTin entity, TXuLyBanTinDTO dto);

    @Named("sDacTaBanTinId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SDacTaBanTinDTO toDtoSDacTaBanTinId(SDacTaBanTin sDacTaBanTin);

    @Named("sKieuNenDuLieuId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SKieuNenDuLieuDTO toDtoSKieuNenDuLieuId(SKieuNenDuLieu sKieuNenDuLieu);

    @Named("sLoiXuLyBanTinId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SLoiXuLyBanTinDTO toDtoSLoiXuLyBanTinId(SLoiXuLyBanTin sLoiXuLyBanTin);

    @Named("tTepDuLieuId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TTepDuLieuDTO toDtoTTepDuLieuId(TTepDuLieu tTepDuLieu);
}

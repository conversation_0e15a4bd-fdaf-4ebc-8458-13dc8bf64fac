package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SCanBoQuanLy;
import com.fds.flex.hbs.dulieuchu.domain.SCapBacTrinhDo;
import com.fds.flex.hbs.dulieuchu.domain.SChuanKyThiDanhGia;
import com.fds.flex.hbs.dulieuchu.domain.SCoQuanDonVi;
import com.fds.flex.hbs.dulieuchu.domain.TChungNhanKetQuaThi;
import com.fds.flex.hbs.dulieuchu.domain.TNguoiHoc;
import com.fds.flex.hbs.dulieuchu.service.dto.SCanBoQuanLyDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SCapBacTrinhDoDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SChuanKyThiDanhGiaDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SCoQuanDonViDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TChungNhanKetQuaThiDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TNguoiHocDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link TChungNhanKetQuaThi} and its DTO {@link TChungNhanKetQuaThiDTO}.
 */
@Mapper(componentModel = "spring")
public interface TChungNhanKetQuaThiMapper extends EntityMapper<TChungNhanKetQuaThiDTO, TChungNhanKetQuaThi> {
    @Mapping(target = "nguoiHoc", source = "nguoiHoc", qualifiedByName = "tNguoiHocId")
    @Mapping(target = "coQuanChungNhan", source = "coQuanChungNhan", qualifiedByName = "sCoQuanDonViId")
    @Mapping(target = "chuanKyThiDanhGia", source = "chuanKyThiDanhGia", qualifiedByName = "sChuanKyThiDanhGiaId")
    @Mapping(target = "danhGiaTrinhDo", source = "danhGiaTrinhDo", qualifiedByName = "sCapBacTrinhDoId")
    @Mapping(target = "nguoiKyChungNhan", source = "nguoiKyChungNhan", qualifiedByName = "sCanBoQuanLyId")
    TChungNhanKetQuaThiDTO toDto(TChungNhanKetQuaThi s);

    @Named("tNguoiHocId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TNguoiHocDTO toDtoTNguoiHocId(TNguoiHoc tNguoiHoc);

    @Named("sCoQuanDonViId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SCoQuanDonViDTO toDtoSCoQuanDonViId(SCoQuanDonVi sCoQuanDonVi);

    @Named("sChuanKyThiDanhGiaId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SChuanKyThiDanhGiaDTO toDtoSChuanKyThiDanhGiaId(SChuanKyThiDanhGia sChuanKyThiDanhGia);

    @Named("sCapBacTrinhDoId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SCapBacTrinhDoDTO toDtoSCapBacTrinhDoId(SCapBacTrinhDo sCapBacTrinhDo);

    @Named("sCanBoQuanLyId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SCanBoQuanLyDTO toDtoSCanBoQuanLyId(SCanBoQuanLy sCanBoQuanLy);
}

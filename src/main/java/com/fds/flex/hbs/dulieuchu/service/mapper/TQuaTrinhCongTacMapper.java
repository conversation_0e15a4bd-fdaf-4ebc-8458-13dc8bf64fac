package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SChucVuCongTac;
import com.fds.flex.hbs.dulieuchu.domain.SCoQuanDonVi;
import com.fds.flex.hbs.dulieuchu.domain.TNhaGiaoCanBo;
import com.fds.flex.hbs.dulieuchu.domain.TQuaTrinhCongTac;
import com.fds.flex.hbs.dulieuchu.service.dto.SChucVuCongTacDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SCoQuanDonViDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TNhaGiaoCanBoDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TQuaTrinhCongTacDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link TQuaTrinhCongTac} and its DTO {@link TQuaTrinhCongTacDTO}.
 */
@Mapper(componentModel = "spring")
public interface TQuaTrinhCongTacMapper extends EntityMapper<TQuaTrinhCongTacDTO, TQuaTrinhCongTac> {
    @Mapping(target = "nhaGiaoCanBo", source = "nhaGiaoCanBo", qualifiedByName = "tNhaGiaoCanBoId")
    @Mapping(target = "noiCongTac", source = "noiCongTac", qualifiedByName = "sCoQuanDonViId")
    @Mapping(target = "chucVuCongTac", source = "chucVuCongTac", qualifiedByName = "sChucVuCongTacId")
    TQuaTrinhCongTacDTO toDto(TQuaTrinhCongTac s);

    @InheritInverseConfiguration
    @Mapping(target = "nhaGiaoCanBo", ignore = true)
    @Mapping(target = "noiCongTac", ignore = true)
    @Mapping(target = "chucVuCongTac", ignore = true)
    TQuaTrinhCongTac toEntity(TQuaTrinhCongTacDTO tQuaTrinhCongTacDTO);

    @InheritInverseConfiguration
    @Mapping(target = "nhaGiaoCanBo", ignore = true)
    @Mapping(target = "noiCongTac", ignore = true)
    @Mapping(target = "chucVuCongTac", ignore = true)
    void partialUpdate(@MappingTarget TQuaTrinhCongTac entity, TQuaTrinhCongTacDTO dto);

    @Named("tNhaGiaoCanBoId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TNhaGiaoCanBoDTO toDtoTNhaGiaoCanBoId(TNhaGiaoCanBo tNhaGiaoCanBo);

    @Named("sCoQuanDonViId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SCoQuanDonViDTO toDtoSCoQuanDonViId(SCoQuanDonVi sCoQuanDonVi);

    @Named("sChucVuCongTacId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SChucVuCongTacDTO toDtoSChucVuCongTacId(SChucVuCongTac sChucVuCongTac);
}

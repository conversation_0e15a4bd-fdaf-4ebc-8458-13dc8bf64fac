package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SVanBan;
import com.fds.flex.hbs.dulieuchu.domain.TCoSoGiaoDuc;
import com.fds.flex.hbs.dulieuchu.domain.TQuaTrinhCongTac;
import com.fds.flex.hbs.dulieuchu.domain.TVanBangChungChi;
import com.fds.flex.hbs.dulieuchu.service.dto.SVanBanDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TCoSoGiaoDucDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TQuaTrinhCongTacDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TVanBangChungChiDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link SVanBan} and its DTO {@link SVanBanDTO}.
 */
@Mapper(componentModel = "spring")
public interface SVanBanMapper extends EntityMapper<SVanBanDTO, SVanBan> {
    @Mapping(target = "tCoSoGiaoDuc", source = "tCoSoGiaoDuc", qualifiedByName = "tCoSoGiaoDucId")
    @Mapping(target = "tQuaTrinhCongTac", source = "tQuaTrinhCongTac", qualifiedByName = "tQuaTrinhCongTacId")
    @Mapping(target = "tVanBangChungChi", source = "tVanBangChungChi", qualifiedByName = "tVanBangChungChiId")
    SVanBanDTO toDto(SVanBan s);

    @Named("tCoSoGiaoDucId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TCoSoGiaoDucDTO toDtoTCoSoGiaoDucId(TCoSoGiaoDuc tCoSoGiaoDuc);

    @Named("tQuaTrinhCongTacId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TQuaTrinhCongTacDTO toDtoTQuaTrinhCongTacId(TQuaTrinhCongTac tQuaTrinhCongTac);

    @Named("tVanBangChungChiId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TVanBangChungChiDTO toDtoTVanBangChungChiId(TVanBangChungChi tVanBangChungChi);
}

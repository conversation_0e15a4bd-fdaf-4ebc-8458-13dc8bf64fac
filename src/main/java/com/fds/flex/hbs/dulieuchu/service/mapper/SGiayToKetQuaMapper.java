package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SGiayToKetQua;
import com.fds.flex.hbs.dulieuchu.domain.TTepDuLieu;
import com.fds.flex.hbs.dulieuchu.service.dto.SGiayToKetQuaDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TTepDuLieuDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link SGiayToKetQua} and its DTO {@link SGiayToKetQuaDTO}.
 */
@Mapper(componentModel = "spring")
public interface SGiayToKetQuaMapper extends EntityMapper<SGiayToKetQuaDTO, SGiayToKetQua> {
    @Mapping(target = "tepDuLieu", source = "tepDuLieu", qualifiedByName = "tTepDuLieuId")
    SGiayToKetQuaDTO toDto(SGiayToKetQua s);

    @Named("tTepDuLieuId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TTepDuLieuDTO toDtoTTepDuLieuId(TTepDuLieu tTepDuLieu);
}

package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SChuongTrinhGiaoDuc;
import com.fds.flex.hbs.dulieuchu.domain.SDiaChi;
import com.fds.flex.hbs.dulieuchu.domain.SLoaiCoSoGiaoDuc;
import com.fds.flex.hbs.dulieuchu.domain.SLoaiHinhTruong;
import com.fds.flex.hbs.dulieuchu.domain.SNhaDauTu;
import com.fds.flex.hbs.dulieuchu.domain.TCoQuanQuanLy;
import com.fds.flex.hbs.dulieuchu.domain.TCoSoGiaoDuc;
import com.fds.flex.hbs.dulieuchu.domain.TNguoiHoc;
import com.fds.flex.hbs.dulieuchu.service.dto.SChuongTrinhGiaoDucDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SDiaChiDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SLoaiCoSoGiaoDucDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SLoaiHinhTruongDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SNhaDauTuDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TCoQuanQuanLyDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TCoSoGiaoDucDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TNguoiHocDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link TCoSoGiaoDuc} and its DTO {@link TCoSoGiaoDucDTO}.
 */
@Mapper(componentModel = "spring")
public interface TCoSoGiaoDucMapper extends EntityMapper<TCoSoGiaoDucDTO, TCoSoGiaoDuc> {
    @Mapping(target = "diaChi", source = "diaChi", qualifiedByName = "sDiaChiId")
    @Mapping(target = "coQuanQuanLy", source = "coQuanQuanLy", qualifiedByName = "tCoQuanQuanLyId")
    @Mapping(target = "loaiHinhTruong", source = "loaiHinhTruong", qualifiedByName = "sLoaiHinhTruongId")
    @Mapping(target = "loaiCoSoGiaoDuc", source = "loaiCoSoGiaoDuc", qualifiedByName = "sLoaiCoSoGiaoDucId")
    @Mapping(target = "chuongTrinhGiaoDuc", source = "chuongTrinhGiaoDuc", qualifiedByName = "sChuongTrinhGiaoDucId")
    @Mapping(target = "nhaDauTu", source = "nhaDauTu", qualifiedByName = "sNhaDauTuId")
    @Mapping(target = "tNguoiHoc", source = "tNguoiHoc", qualifiedByName = "tNguoiHocId")
    TCoSoGiaoDucDTO toDto(TCoSoGiaoDuc s);

    @InheritInverseConfiguration
    @Mapping(target = "diaChi", ignore = true)
    @Mapping(target = "coQuanQuanLy", ignore = true)
    @Mapping(target = "loaiHinhTruong", ignore = true)
    @Mapping(target = "loaiCoSoGiaoDuc", ignore = true)
    @Mapping(target = "chuongTrinhGiaoDuc", ignore = true)
    @Mapping(target = "nhaDauTu", ignore = true)
    @Mapping(target = "tNguoiHoc", ignore = true)
    TCoSoGiaoDuc toEntity(TCoSoGiaoDucDTO tCoSoGiaoDucDTO);

    @InheritInverseConfiguration
    @Mapping(target = "diaChi", ignore = true)
    @Mapping(target = "coQuanQuanLy", ignore = true)
    @Mapping(target = "loaiHinhTruong", ignore = true)
    @Mapping(target = "loaiCoSoGiaoDuc", ignore = true)
    @Mapping(target = "chuongTrinhGiaoDuc", ignore = true)
    @Mapping(target = "nhaDauTu", ignore = true)
    @Mapping(target = "tNguoiHoc", ignore = true)
    void partialUpdate(@MappingTarget TCoSoGiaoDuc entity, TCoSoGiaoDucDTO dto);

    @Named("sDiaChiId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SDiaChiDTO toDtoSDiaChiId(SDiaChi sDiaChi);

    @Named("tCoQuanQuanLyId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TCoQuanQuanLyDTO toDtoTCoQuanQuanLyId(TCoQuanQuanLy tCoQuanQuanLy);

    @Named("sLoaiHinhTruongId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SLoaiHinhTruongDTO toDtoSLoaiHinhTruongId(SLoaiHinhTruong sLoaiHinhTruong);

    @Named("sLoaiCoSoGiaoDucId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SLoaiCoSoGiaoDucDTO toDtoSLoaiCoSoGiaoDucId(SLoaiCoSoGiaoDuc sLoaiCoSoGiaoDuc);

    @Named("sChuongTrinhGiaoDucId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SChuongTrinhGiaoDucDTO toDtoSChuongTrinhGiaoDucId(SChuongTrinhGiaoDuc sChuongTrinhGiaoDuc);

    @Named("sNhaDauTuId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SNhaDauTuDTO toDtoSNhaDauTuId(SNhaDauTu sNhaDauTu);

    @Named("tNguoiHocId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TNguoiHocDTO toDtoTNguoiHocId(TNguoiHoc tNguoiHoc);
}

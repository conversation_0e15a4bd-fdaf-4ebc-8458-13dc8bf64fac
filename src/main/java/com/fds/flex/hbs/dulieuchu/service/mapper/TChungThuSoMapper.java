package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SDoiTuongThueBao;
import com.fds.flex.hbs.dulieuchu.domain.SThongTinChungThu;
import com.fds.flex.hbs.dulieuchu.domain.TChungThuSo;
import com.fds.flex.hbs.dulieuchu.service.dto.SDoiTuongThueBaoDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SThongTinChungThuDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TChungThuSoDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link TChungThuSo} and its DTO {@link TChungThuSoDTO}.
 */
@Mapper(componentModel = "spring")
public interface TChungThuSoMapper extends EntityMapper<TChungThuSoDTO, TChungThuSo> {
    @Mapping(target = "doiTuongThueBao", source = "doiTuongThueBao", qualifiedByName = "sDoiTuongThueBaoId")
    @Mapping(target = "thongTinChungThu", source = "thongTinChungThu", qualifiedByName = "sThongTinChungThuId")
    TChungThuSoDTO toDto(TChungThuSo s);

    @Named("sDoiTuongThueBaoId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SDoiTuongThueBaoDTO toDtoSDoiTuongThueBaoId(SDoiTuongThueBao sDoiTuongThueBao);

    @Named("sThongTinChungThuId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SThongTinChungThuDTO toDtoSThongTinChungThuId(SThongTinChungThu sThongTinChungThu);
}

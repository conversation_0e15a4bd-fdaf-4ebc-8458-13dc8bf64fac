package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SChuanChuongTrinh;
import com.fds.flex.hbs.dulieuchu.domain.TCoSoGiaoDuc;
import com.fds.flex.hbs.dulieuchu.service.dto.SChuanChuongTrinhDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TCoSoGiaoDucDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link SChuanChuongTrinh} and its DTO {@link SChuanChuongTrinhDTO}.
 */
@Mapper(componentModel = "spring")
public interface SChuanChuongTrinhMapper extends EntityMapper<SChuanChuongTrinhDTO, SChuanChuongTrinh> {
    @Mapping(target = "tCoSoGiaoDuc", source = "tCoSoGiaoDuc", qualifiedByName = "tCoSoGiaoDucId")
    SChuanChuongTrinhDTO toDto(SChuanChuongTrinh s);

    @Named("tCoSoGiaoDucId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TCoSoGiaoDucDTO toDtoTCoSoGiaoDucId(TCoSoGiaoDuc tCoSoGiaoDuc);
}

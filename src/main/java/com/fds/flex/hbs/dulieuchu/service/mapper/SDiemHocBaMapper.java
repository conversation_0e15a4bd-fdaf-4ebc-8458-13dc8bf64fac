package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SDiemHocBa;
import com.fds.flex.hbs.dulieuchu.domain.SGiaoVien;
import com.fds.flex.hbs.dulieuchu.domain.THocBa;
import com.fds.flex.hbs.dulieuchu.service.dto.SDiemHocBaDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SGiaoVienDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.THocBaDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link SDiemHocBa} and its DTO {@link SDiemHocBaDTO}.
 */
@Mapper(componentModel = "spring")
public interface SDiemHocBaMapper extends EntityMapper<SDiemHocBaDTO, SDiemHocBa> {
    @Mapping(target = "giaoVienBoMon", source = "giaoVienBoMon", qualifiedByName = "sGiaoVienId")
    @Mapping(target = "tHocBa", source = "tHocBa", qualifiedByName = "tHocBaId")
    SDiemHocBaDTO toDto(SDiemHocBa s);

    @Named("sGiaoVienId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SGiaoVienDTO toDtoSGiaoVienId(SGiaoVien sGiaoVien);

    @Named("tHocBaId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    THocBaDTO toDtoTHocBaId(THocBa tHocBa);
}

package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SCanBoQuanLy;
import com.fds.flex.hbs.dulieuchu.domain.SChuanKyThiThanhTich;
import com.fds.flex.hbs.dulieuchu.domain.SCoQuanDonVi;
import com.fds.flex.hbs.dulieuchu.domain.SMonThiThanhTich;
import com.fds.flex.hbs.dulieuchu.domain.SMucGiaiThuong;
import com.fds.flex.hbs.dulieuchu.domain.TChungNhanThanhTich;
import com.fds.flex.hbs.dulieuchu.domain.TNguoiHoc;
import com.fds.flex.hbs.dulieuchu.service.dto.SCanBoQuanLyDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SChuanKyThiThanhTichDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SCoQuanDonViDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SMonThiThanhTichDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SMucGiaiThuongDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TChungNhanThanhTichDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TNguoiHocDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link TChungNhanThanhTich} and its DTO {@link TChungNhanThanhTichDTO}.
 */
@Mapper(componentModel = "spring")
public interface TChungNhanThanhTichMapper extends EntityMapper<TChungNhanThanhTichDTO, TChungNhanThanhTich> {
    @Mapping(target = "nguoiHoc", source = "nguoiHoc", qualifiedByName = "tNguoiHocId")
    @Mapping(target = "coQuanChungNhan", source = "coQuanChungNhan", qualifiedByName = "sCoQuanDonViId")
    @Mapping(target = "chuanKyThiThanhTich", source = "chuanKyThiThanhTich", qualifiedByName = "sChuanKyThiThanhTichId")
    @Mapping(target = "monThiThanhTich", source = "monThiThanhTich", qualifiedByName = "sMonThiThanhTichId")
    @Mapping(target = "mucGiaiThuong", source = "mucGiaiThuong", qualifiedByName = "sMucGiaiThuongId")
    @Mapping(target = "nguoiKyChungNhan", source = "nguoiKyChungNhan", qualifiedByName = "sCanBoQuanLyId")
    TChungNhanThanhTichDTO toDto(TChungNhanThanhTich s);

    @Named("tNguoiHocId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TNguoiHocDTO toDtoTNguoiHocId(TNguoiHoc tNguoiHoc);

    @Named("sCoQuanDonViId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SCoQuanDonViDTO toDtoSCoQuanDonViId(SCoQuanDonVi sCoQuanDonVi);

    @Named("sChuanKyThiThanhTichId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SChuanKyThiThanhTichDTO toDtoSChuanKyThiThanhTichId(SChuanKyThiThanhTich sChuanKyThiThanhTich);

    @Named("sMonThiThanhTichId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SMonThiThanhTichDTO toDtoSMonThiThanhTichId(SMonThiThanhTich sMonThiThanhTich);

    @Named("sMucGiaiThuongId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SMucGiaiThuongDTO toDtoSMucGiaiThuongId(SMucGiaiThuong sMucGiaiThuong);

    @Named("sCanBoQuanLyId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SCanBoQuanLyDTO toDtoSCanBoQuanLyId(SCanBoQuanLy sCanBoQuanLy);
}

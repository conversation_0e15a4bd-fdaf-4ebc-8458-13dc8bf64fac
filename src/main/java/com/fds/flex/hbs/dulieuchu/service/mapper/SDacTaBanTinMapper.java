package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SCanBoQuanLy;
import com.fds.flex.hbs.dulieuchu.domain.SCoQuanDonVi;
import com.fds.flex.hbs.dulieuchu.domain.SDacTaBanTin;
import com.fds.flex.hbs.dulieuchu.service.dto.SCanBoQuanLyDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SCoQuanDonViDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SDacTaBanTinDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link SDacTaBanTin} and its DTO {@link SDacTaBanTinDTO}.
 */
@Mapper(componentModel = "spring")
public interface SDacTaBanTinMapper extends EntityMapper<SDacTaBanTinDTO, SDacTaBanTin> {
    @Mapping(target = "noiTaoBanTin", source = "noiTaoBanTin", qualifiedByName = "sCoQuanDonViId")
    @Mapping(target = "noiNhanBanTin", source = "noiNhanBanTin", qualifiedByName = "sCoQuanDonViId")
    @Mapping(target = "canBoQuanLy", source = "canBoQuanLy", qualifiedByName = "sCanBoQuanLyId")
    SDacTaBanTinDTO toDto(SDacTaBanTin s);

    @Named("sCoQuanDonViId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SCoQuanDonViDTO toDtoSCoQuanDonViId(SCoQuanDonVi sCoQuanDonVi);

    @Named("sCanBoQuanLyId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SCanBoQuanLyDTO toDtoSCanBoQuanLyId(SCanBoQuanLy sCanBoQuanLy);
}

package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SMaDinhDanhDuLieu;
import com.fds.flex.hbs.dulieuchu.domain.TKetQuaXuLy;
import com.fds.flex.hbs.dulieuchu.service.dto.SMaDinhDanhDuLieuDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TKetQuaXuLyDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link TKetQuaXuLy} and its DTO {@link TKetQuaXuLyDTO}.
 */
@Mapper(componentModel = "spring")
public interface TKetQuaXuLyMapper extends EntityMapper<TKetQuaXuLyDTO, TKetQuaXuLy> {
    @Mapping(target = "maDinhDanhDuLieu", source = "maDinhDanhDuLieu", qualifiedByName = "sMaDinhDanhDuLieuId")
    TKetQuaXuLyDTO toDto(TKetQuaXuLy s);

    @Named("sMaDinhDanhDuLieuId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SMaDinhDanhDuLieuDTO toDtoSMaDinhDanhDuLieuId(SMaDinhDanhDuLieu sMaDinhDanhDuLieu);
}

package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SCapBacTrinhDo;
import com.fds.flex.hbs.dulieuchu.domain.SChucDanhNgheNghiep;
import com.fds.flex.hbs.dulieuchu.domain.SChucVuCongTac;
import com.fds.flex.hbs.dulieuchu.domain.SCoQuanDonVi;
import com.fds.flex.hbs.dulieuchu.domain.SDanToc;
import com.fds.flex.hbs.dulieuchu.domain.SDiaChi;
import com.fds.flex.hbs.dulieuchu.domain.SKhoiNganhDaoTao;
import com.fds.flex.hbs.dulieuchu.domain.SQuocTich;
import com.fds.flex.hbs.dulieuchu.domain.STinhTrangHonNhan;
import com.fds.flex.hbs.dulieuchu.domain.STonGiao;
import com.fds.flex.hbs.dulieuchu.domain.TCoQuanQuanLy;
import com.fds.flex.hbs.dulieuchu.domain.TNhaGiaoCanBo;
import com.fds.flex.hbs.dulieuchu.service.dto.SCapBacTrinhDoDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SChucDanhNgheNghiepDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SChucVuCongTacDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SCoQuanDonViDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SDanTocDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SDiaChiDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SKhoiNganhDaoTaoDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SQuocTichDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.STinhTrangHonNhanDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.STonGiaoDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TCoQuanQuanLyDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TNhaGiaoCanBoDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link TNhaGiaoCanBo} and its DTO {@link TNhaGiaoCanBoDTO}.
 */
@Mapper(componentModel = "spring")
public interface TNhaGiaoCanBoMapper extends EntityMapper<TNhaGiaoCanBoDTO, TNhaGiaoCanBo> {
    @Mapping(target = "danToc", source = "danToc", qualifiedByName = "sDanTocId")
    @Mapping(target = "tonGiao", source = "tonGiao", qualifiedByName = "sTonGiaoId")
    @Mapping(target = "tinhTrangHonNhan", source = "tinhTrangHonNhan", qualifiedByName = "sTinhTrangHonNhanId")
    @Mapping(target = "queQuan", source = "queQuan", qualifiedByName = "sDiaChiId")
    @Mapping(target = "diaChiThuongTru", source = "diaChiThuongTru", qualifiedByName = "sDiaChiId")
    @Mapping(target = "noiOHienNay", source = "noiOHienNay", qualifiedByName = "sDiaChiId")
    @Mapping(target = "quocTich", source = "quocTich", qualifiedByName = "sQuocTichId")
    @Mapping(target = "trinhDoChuyenMon", source = "trinhDoChuyenMon", qualifiedByName = "sCapBacTrinhDoId")
    @Mapping(target = "khoiNganhDaoTao", source = "khoiNganhDaoTao", qualifiedByName = "sKhoiNganhDaoTaoId")
    @Mapping(target = "trinhDoQuanLyGiaoDuc", source = "trinhDoQuanLyGiaoDuc", qualifiedByName = "sCapBacTrinhDoId")
    @Mapping(target = "chucDanhNgheNghiep", source = "chucDanhNgheNghiep", qualifiedByName = "sChucDanhNgheNghiepId")
    @Mapping(target = "chucVuCongTac", source = "chucVuCongTac", qualifiedByName = "sChucVuCongTacId")
    @Mapping(target = "noiCongTacHienTai", source = "noiCongTacHienTai", qualifiedByName = "sCoQuanDonViId")
    @Mapping(target = "coQuanChuQuan", source = "coQuanChuQuan", qualifiedByName = "tCoQuanQuanLyId")
    TNhaGiaoCanBoDTO toDto(TNhaGiaoCanBo s);


    @InheritInverseConfiguration
    @Mapping(target = "danToc", ignore = true)
    @Mapping(target = "tonGiao", ignore = true)
    @Mapping(target = "tinhTrangHonNhan", ignore = true)
    @Mapping(target = "queQuan", ignore = true)
    @Mapping(target = "diaChiThuongTru", ignore = true)
    @Mapping(target = "noiOHienNay", ignore = true)
    @Mapping(target = "quocTich", ignore = true)
    @Mapping(target = "trinhDoChuyenMon", ignore = true)
    @Mapping(target = "khoiNganhDaoTao", ignore = true)
    @Mapping(target = "trinhDoQuanLyGiaoDuc", ignore = true)
    @Mapping(target = "chucDanhNgheNghiep", ignore = true)
    @Mapping(target = "chucVuCongTac", ignore = true)
    @Mapping(target = "noiCongTacHienTai", ignore = true)
    @Mapping(target = "coQuanChuQuan", ignore = true)
    TNhaGiaoCanBo toEntity(TNhaGiaoCanBoDTO tNhaGiaoCanBoDTO);

    @InheritInverseConfiguration
    @Mapping(target = "danToc", ignore = true)
    @Mapping(target = "tonGiao", ignore = true)
    @Mapping(target = "tinhTrangHonNhan", ignore = true)
    @Mapping(target = "queQuan", ignore = true)
    @Mapping(target = "diaChiThuongTru", ignore = true)
    @Mapping(target = "noiOHienNay", ignore = true)
    @Mapping(target = "quocTich", ignore = true)
    @Mapping(target = "trinhDoChuyenMon", ignore = true)
    @Mapping(target = "khoiNganhDaoTao", ignore = true)
    @Mapping(target = "trinhDoQuanLyGiaoDuc", ignore = true)
    @Mapping(target = "chucDanhNgheNghiep", ignore = true)
    @Mapping(target = "chucVuCongTac", ignore = true)
    @Mapping(target = "noiCongTacHienTai", ignore = true)
    @Mapping(target = "coQuanChuQuan", ignore = true)
    void partialUpdate(@MappingTarget TNhaGiaoCanBo entity, TNhaGiaoCanBoDTO dto);
    

    @Named("sDanTocId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SDanTocDTO toDtoSDanTocId(SDanToc sDanToc);

    @Named("sTonGiaoId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    STonGiaoDTO toDtoSTonGiaoId(STonGiao sTonGiao);

    @Named("sTinhTrangHonNhanId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    STinhTrangHonNhanDTO toDtoSTinhTrangHonNhanId(STinhTrangHonNhan sTinhTrangHonNhan);

    @Named("sDiaChiId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SDiaChiDTO toDtoSDiaChiId(SDiaChi sDiaChi);

    @Named("sQuocTichId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SQuocTichDTO toDtoSQuocTichId(SQuocTich sQuocTich);

    @Named("sCapBacTrinhDoId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SCapBacTrinhDoDTO toDtoSCapBacTrinhDoId(SCapBacTrinhDo sCapBacTrinhDo);

    @Named("sKhoiNganhDaoTaoId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SKhoiNganhDaoTaoDTO toDtoSKhoiNganhDaoTaoId(SKhoiNganhDaoTao sKhoiNganhDaoTao);

    @Named("sChucDanhNgheNghiepId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SChucDanhNgheNghiepDTO toDtoSChucDanhNgheNghiepId(SChucDanhNgheNghiep sChucDanhNgheNghiep);

    @Named("sChucVuCongTacId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SChucVuCongTacDTO toDtoSChucVuCongTacId(SChucVuCongTac sChucVuCongTac);

    @Named("sCoQuanDonViId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SCoQuanDonViDTO toDtoSCoQuanDonViId(SCoQuanDonVi sCoQuanDonVi);

    @Named("tCoQuanQuanLyId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TCoQuanQuanLyDTO toDtoTCoQuanQuanLyId(TCoQuanQuanLy tCoQuanQuanLy);
}

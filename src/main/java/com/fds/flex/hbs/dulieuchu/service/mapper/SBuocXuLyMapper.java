package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SBuocXuLy;
import com.fds.flex.hbs.dulieuchu.domain.SQuaTrinhXuLy;
import com.fds.flex.hbs.dulieuchu.service.dto.SBuocXuLyDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SQuaTrinhXuLyDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link SBuocXuLy} and its DTO {@link SBuocXuLyDTO}.
 */
@Mapper(componentModel = "spring")
public interface SBuocXuLyMapper extends EntityMapper<SBuocXuLyDTO, SBuocXuLy> {
    @Mapping(target = "sQuaTrinhXuLy", source = "sQuaTrinhXuLy", qualifiedByName = "sQuaTrinhXuLyId")
    SBuocXuLyDTO toDto(SBuocXuLy s);

    @Named("sQuaTrinhXuLyId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SQuaTrinhXuLyDTO toDtoSQuaTrinhXuLyId(SQuaTrinhXuLy sQuaTrinhXuLy);
}

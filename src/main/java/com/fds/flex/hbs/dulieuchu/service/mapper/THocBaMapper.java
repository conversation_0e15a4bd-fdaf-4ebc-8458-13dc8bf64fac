package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SCanBoQuanLy;
import com.fds.flex.hbs.dulieuchu.domain.SCapHoc;
import com.fds.flex.hbs.dulieuchu.domain.SGiaoVien;
import com.fds.flex.hbs.dulieuchu.domain.SKhoiLopHoc;
import com.fds.flex.hbs.dulieuchu.domain.STongKetHocBa;
import com.fds.flex.hbs.dulieuchu.domain.TCoSoGiaoDuc;
import com.fds.flex.hbs.dulieuchu.domain.THocBa;
import com.fds.flex.hbs.dulieuchu.domain.TNguoiHoc;
import com.fds.flex.hbs.dulieuchu.service.dto.SCanBoQuanLyDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SCapHocDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SGiaoVienDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SKhoiLopHocDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.STongKetHocBaDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TCoSoGiaoDucDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.THocBaDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TNguoiHocDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link THocBa} and its DTO {@link THocBaDTO}.
 */
@Mapper(componentModel = "spring")
public interface THocBaMapper extends EntityMapper<THocBaDTO, THocBa> {
    @Mapping(target = "coSoGiaoDuc", source = "coSoGiaoDuc", qualifiedByName = "tCoSoGiaoDucId")
    @Mapping(target = "capHoc", source = "capHoc", qualifiedByName = "sCapHocId")
    @Mapping(target = "nguoiHoc", source = "nguoiHoc", qualifiedByName = "tNguoiHocId")
    @Mapping(target = "khoiLopHoc", source = "khoiLopHoc", qualifiedByName = "sKhoiLopHocId")
    @Mapping(target = "giaoVienChuNhiem", source = "giaoVienChuNhiem", qualifiedByName = "sGiaoVienId")
    @Mapping(target = "giamHieuKyHocBa", source = "giamHieuKyHocBa", qualifiedByName = "sCanBoQuanLyId")
    @Mapping(target = "tongKetHocBa", source = "tongKetHocBa", qualifiedByName = "sTongKetHocBaId")
    THocBaDTO toDto(THocBa s);

    @Named("tCoSoGiaoDucId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TCoSoGiaoDucDTO toDtoTCoSoGiaoDucId(TCoSoGiaoDuc tCoSoGiaoDuc);

//    @Named("sCapHocId")
//    @BeanMapping(ignoreByDefault = true)
//    @Mapping(target = "id", source = "id")
//    SCapHocDTO toDtoSCapHocId(SCapHoc sCapHoc);

    @Named("tNguoiHocId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TNguoiHocDTO toDtoTNguoiHocId(TNguoiHoc tNguoiHoc);

    @Named("sKhoiLopHocId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SKhoiLopHocDTO toDtoSKhoiLopHocId(SKhoiLopHoc sKhoiLopHoc);

    @Named("sGiaoVienId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SGiaoVienDTO toDtoSGiaoVienId(SGiaoVien sGiaoVien);

    @Named("sCanBoQuanLyId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SCanBoQuanLyDTO toDtoSCanBoQuanLyId(SCanBoQuanLy sCanBoQuanLy);

    @Named("sTongKetHocBaId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    STongKetHocBaDTO toDtoSTongKetHocBaId(STongKetHocBa sTongKetHocBa);
}

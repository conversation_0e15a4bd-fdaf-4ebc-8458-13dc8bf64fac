package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SChuKySo;
import com.fds.flex.hbs.dulieuchu.domain.SDoiTuongThueBao;
import com.fds.flex.hbs.dulieuchu.domain.TChungThuSo;
import com.fds.flex.hbs.dulieuchu.domain.TTepDuLieu;
import com.fds.flex.hbs.dulieuchu.service.dto.SChuKySoDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SDoiTuongThueBaoDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TChungThuSoDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TTepDuLieuDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link SChuKySo} and its DTO {@link SChuKySoDTO}.
 */
@Mapper(componentModel = "spring")
public interface SChuKySoMapper extends EntityMapper<SChuKySoDTO, SChuKySo> {
    @Mapping(target = "soChungThu", source = "soChungThu", qualifiedByName = "tChungThuSoId")
    @Mapping(target = "doiTuongThueBao", source = "doiTuongThueBao", qualifiedByName = "sDoiTuongThueBaoId")
    @Mapping(target = "tTepDuLieu", source = "tTepDuLieu", qualifiedByName = "tTepDuLieuId")
    SChuKySoDTO toDto(SChuKySo s);

    @Named("tChungThuSoId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TChungThuSoDTO toDtoTChungThuSoId(TChungThuSo tChungThuSo);

    @Named("sDoiTuongThueBaoId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SDoiTuongThueBaoDTO toDtoSDoiTuongThueBaoId(SDoiTuongThueBao sDoiTuongThueBao);

    @Named("tTepDuLieuId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TTepDuLieuDTO toDtoTTepDuLieuId(TTepDuLieu tTepDuLieu);
}

package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SNangLucPhamChat;
import com.fds.flex.hbs.dulieuchu.domain.THocBa;
import com.fds.flex.hbs.dulieuchu.service.dto.SNangLucPhamChatDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.THocBaDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link SNangLucPhamChat} and its DTO {@link SNangLucPhamChatDTO}.
 */
@Mapper(componentModel = "spring")
public interface SNangLucPhamChatMapper extends EntityMapper<SNangLucPhamChatDTO, SNangLucPhamChat> {
    @Mapping(target = "tHocBa", source = "tHocBa", qualifiedByName = "tHocBaId")
    SNangLucPhamChatDTO toDto(SNangLucPhamChat s);

    @Named("tHocBaId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    THocBaDTO toDtoTHocBaId(THocBa tHocBa);
}

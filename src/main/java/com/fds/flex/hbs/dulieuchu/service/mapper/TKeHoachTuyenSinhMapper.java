package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SCapBacTrinhDo;
import com.fds.flex.hbs.dulieuchu.domain.SCapHoc;
import com.fds.flex.hbs.dulieuchu.domain.SChuongTrinhGiaoDuc;
import com.fds.flex.hbs.dulieuchu.domain.SHinhThucDaoTao;
import com.fds.flex.hbs.dulieuchu.domain.TCoSoGiaoDuc;
import com.fds.flex.hbs.dulieuchu.domain.TKeHoachTuyenSinh;
import com.fds.flex.hbs.dulieuchu.service.dto.SCapBacTrinhDoDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SCapHocDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SChuongTrinhGiaoDucDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SHinhThucDaoTaoDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TCoSoGiaoDucDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TKeHoachTuyenSinhDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link TKeHoachTuyenSinh} and its DTO {@link TKeHoachTuyenSinhDTO}.
 */
@Mapper(componentModel = "spring")
public interface TKeHoachTuyenSinhMapper extends EntityMapper<TKeHoachTuyenSinhDTO, TKeHoachTuyenSinh> {
    @Mapping(target = "coSoGiaoDuc", source = "coSoGiaoDuc", qualifiedByName = "tCoSoGiaoDucId")
    @Mapping(target = "chuongTrinhGiaoDuc", source = "chuongTrinhGiaoDuc", qualifiedByName = "sChuongTrinhGiaoDucId")
    @Mapping(target = "capHoc", source = "capHoc", qualifiedByName = "sCapHocId")
    @Mapping(target = "capBacTrinhDo", source = "capBacTrinhDo", qualifiedByName = "sCapBacTrinhDoId")
    @Mapping(target = "hinhThucDaoTao", source = "hinhThucDaoTao", qualifiedByName = "sHinhThucDaoTaoId")
    TKeHoachTuyenSinhDTO toDto(TKeHoachTuyenSinh s);

    @InheritInverseConfiguration
    @Mapping(target = "coSoGiaoDuc", ignore = true)
    @Mapping(target = "chuongTrinhGiaoDuc", ignore = true)
    @Mapping(target = "capHoc", ignore = true)
    @Mapping(target = "capBacTrinhDo", ignore = true)
    @Mapping(target = "hinhThucDaoTao", ignore = true)
    TKeHoachTuyenSinh toEntity(TKeHoachTuyenSinhDTO tKeHoachTuyenSinhDTO);

    @InheritInverseConfiguration
    @Mapping(target = "coSoGiaoDuc", ignore = true)
    @Mapping(target = "chuongTrinhGiaoDuc", ignore = true)
    @Mapping(target = "capHoc", ignore = true)
    @Mapping(target = "capBacTrinhDo", ignore = true)
    @Mapping(target = "hinhThucDaoTao", ignore = true)
    void partialUpdate(@MappingTarget TKeHoachTuyenSinh entity, TKeHoachTuyenSinhDTO dto);

    @Named("tCoSoGiaoDucId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TCoSoGiaoDucDTO toDtoTCoSoGiaoDucId(TCoSoGiaoDuc tCoSoGiaoDuc);

    @Named("sChuongTrinhGiaoDucId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SChuongTrinhGiaoDucDTO toDtoSChuongTrinhGiaoDucId(SChuongTrinhGiaoDuc sChuongTrinhGiaoDuc);

    @Named("sCapHocId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SCapHocDTO toDtoSCapHocId(SCapHoc sCapHoc);

    @Named("sCapBacTrinhDoId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SCapBacTrinhDoDTO toDtoSCapBacTrinhDoId(SCapBacTrinhDo sCapBacTrinhDo);

    @Named("sHinhThucDaoTaoId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SHinhThucDaoTaoDTO toDtoSHinhThucDaoTaoId(SHinhThucDaoTao sHinhThucDaoTao);
}

package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SDiemMonThi;
import com.fds.flex.hbs.dulieuchu.domain.TChungNhanKetQuaThi;
import com.fds.flex.hbs.dulieuchu.service.dto.SDiemMonThiDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TChungNhanKetQuaThiDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link SDiemMonThi} and its DTO {@link SDiemMonThiDTO}.
 */
@Mapper(componentModel = "spring")
public interface SDiemMonThiMapper extends EntityMapper<SDiemMonThiDTO, SDiemMonThi> {
    @Mapping(target = "tChungNhanKetQuaThi", source = "tChungNhanKetQuaThi", qualifiedByName = "tChungNhanKetQuaThiId")
    SDiemMonThiDTO toDto(SDiemMonThi s);

    @Named("tChungNhanKetQuaThiId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TChungNhanKetQuaThiDTO toDtoTChungNhanKetQuaThiId(TChungNhanKetQuaThi tChungNhanKetQuaThi);
}

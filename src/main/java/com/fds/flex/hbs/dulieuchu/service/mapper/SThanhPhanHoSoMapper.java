package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SThanhPhanHoSo;
import com.fds.flex.hbs.dulieuchu.domain.TTepDuLieu;
import com.fds.flex.hbs.dulieuchu.service.dto.SThanhPhanHoSoDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TTepDuLieuDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link SThanhPhanHoSo} and its DTO {@link SThanhPhanHoSoDTO}.
 */
@Mapper(componentModel = "spring")
public interface SThanhPhanHoSoMapper extends EntityMapper<SThanhPhanHoSoDTO, SThanhPhanHoSo> {
    @Mapping(target = "tepDuLieu", source = "tepDuLieu", qualifiedByName = "tTepDuLieuId")
    SThanhPhanHoSoDTO toDto(SThanhPhanHoSo s);

    @Named("tTepDuLieuId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TTepDuLieuDTO toDtoTTepDuLieuId(TTepDuLieu tTepDuLieu);
}

package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SCapBacTrinhDo;
import com.fds.flex.hbs.dulieuchu.domain.SCapHoc;
import com.fds.flex.hbs.dulieuchu.domain.SCongDan;
import com.fds.flex.hbs.dulieuchu.domain.SDanToc;
import com.fds.flex.hbs.dulieuchu.domain.SDiaChi;
import com.fds.flex.hbs.dulieuchu.domain.SDienChinhSach;
import com.fds.flex.hbs.dulieuchu.domain.SKhoiNganhDaoTao;
import com.fds.flex.hbs.dulieuchu.domain.SKhuVucUuTien;
import com.fds.flex.hbs.dulieuchu.domain.SQuocTich;
import com.fds.flex.hbs.dulieuchu.domain.TNguoiHoc;
import com.fds.flex.hbs.dulieuchu.service.dto.SCapBacTrinhDoDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SCapHocDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SCongDanDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SDanTocDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SDiaChiDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SDienChinhSachDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SKhoiNganhDaoTaoDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SKhuVucUuTienDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SQuocTichDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TNguoiHocDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link TNguoiHoc} and its DTO {@link TNguoiHocDTO}.
 */
@Mapper(componentModel = "spring")
public interface TNguoiHocMapper extends EntityMapper<TNguoiHocDTO, TNguoiHoc> {
    @Mapping(target = "noiSinh", source = "noiSinh", qualifiedByName = "sDiaChiId")
    @Mapping(target = "diaChiThuongTru", source = "diaChiThuongTru", qualifiedByName = "sDiaChiId")
    @Mapping(target = "noiOHienTai", source = "noiOHienTai", qualifiedByName = "sDiaChiId")
    @Mapping(target = "danToc", source = "danToc", qualifiedByName = "sDanTocId")
    @Mapping(target = "quocTich", source = "quocTich", qualifiedByName = "sQuocTichId")
    @Mapping(target = "dienChinhSach", source = "dienChinhSach", qualifiedByName = "sDienChinhSachId")
    @Mapping(target = "khuVucUuTien", source = "khuVucUuTien", qualifiedByName = "sKhuVucUuTienId")
    @Mapping(target = "thongTinCha", source = "thongTinCha", qualifiedByName = "sCongDanId")
    @Mapping(target = "thongTinMe", source = "thongTinMe", qualifiedByName = "sCongDanId")
    @Mapping(target = "thongTinNguoiGiamHo", source = "thongTinNguoiGiamHo", qualifiedByName = "sCongDanId")
    @Mapping(target = "capHoc", source = "capHoc", qualifiedByName = "sCapHocId")
    @Mapping(target = "capBacTrinhDo", source = "capBacTrinhDo", qualifiedByName = "sCapBacTrinhDoId")
    @Mapping(target = "khoiNganhDaoTao", source = "khoiNganhDaoTao", qualifiedByName = "sKhoiNganhDaoTaoId")
    TNguoiHocDTO toDto(TNguoiHoc s);

    @Named("sDiaChiId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SDiaChiDTO toDtoSDiaChiId(SDiaChi sDiaChi);

    @Named("sDanTocId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SDanTocDTO toDtoSDanTocId(SDanToc sDanToc);

    @Named("sQuocTichId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SQuocTichDTO toDtoSQuocTichId(SQuocTich sQuocTich);

    @Named("sDienChinhSachId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SDienChinhSachDTO toDtoSDienChinhSachId(SDienChinhSach sDienChinhSach);

    @Named("sKhuVucUuTienId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SKhuVucUuTienDTO toDtoSKhuVucUuTienId(SKhuVucUuTien sKhuVucUuTien);

    @Named("sCongDanId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SCongDanDTO toDtoSCongDanId(SCongDan sCongDan);

    @Named("sCapHocId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SCapHocDTO toDtoSCapHocId(SCapHoc sCapHoc);

    @Named("sCapBacTrinhDoId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SCapBacTrinhDoDTO toDtoSCapBacTrinhDoId(SCapBacTrinhDo sCapBacTrinhDo);

    @Named("sKhoiNganhDaoTaoId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SKhoiNganhDaoTaoDTO toDtoSKhoiNganhDaoTaoId(SKhoiNganhDaoTao sKhoiNganhDaoTao);
}

package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SLoiXuLyBanTin;
import com.fds.flex.hbs.dulieuchu.domain.SNoiDungBanTin;
import com.fds.flex.hbs.dulieuchu.service.dto.SLoiXuLyBanTinDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SNoiDungBanTinDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link SNoiDungBanTin} and its DTO {@link SNoiDungBanTinDTO}.
 */
@Mapper(componentModel = "spring")
public interface SNoiDungBanTinMapper extends EntityMapper<SNoiDungBanTinDTO, SNoiDungBanTin> {
    @Mapping(target = "loiXuLyBanTin", source = "loiXuLyBanTin", qualifiedByName = "sLoiXuLyBanTinId")
    SNoiDungBanTinDTO toDto(SNoiDungBanTin s);

    @Named("sLoiXuLyBanTinId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SLoiXuLyBanTinDTO toDtoSLoiXuLyBanTinId(SLoiXuLyBanTin sLoiXuLyBanTin);
}

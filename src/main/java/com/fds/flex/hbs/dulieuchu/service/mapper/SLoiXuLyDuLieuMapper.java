package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SLoiXuLyDuLieu;
import com.fds.flex.hbs.dulieuchu.domain.TKetQuaXuLy;
import com.fds.flex.hbs.dulieuchu.service.dto.SLoiXuLyDuLieuDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TKetQuaXuLyDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link SLoiXuLyDuLieu} and its DTO {@link SLoiXuLyDuLieuDTO}.
 */
@Mapper(componentModel = "spring")
public interface SLoiXuLyDuLieuMapper extends EntityMapper<SLoiXuLyDuLieuDTO, SLoiXuLyDuLieu> {
    @Mapping(target = "tKetQuaXuLy", source = "tKetQuaXuLy", qualifiedByName = "tKetQuaXuLyId")
    SLoiXuLyDuLieuDTO toDto(SLoiXuLyDuLieu s);

    @Named("tKetQuaXuLyId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TKetQuaXuLyDTO toDtoTKetQuaXuLyId(TKetQuaXuLy tKetQuaXuLy);
}

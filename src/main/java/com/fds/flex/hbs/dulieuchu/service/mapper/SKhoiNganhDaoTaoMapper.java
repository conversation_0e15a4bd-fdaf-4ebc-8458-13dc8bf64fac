package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SChuanChuongTrinh;
import com.fds.flex.hbs.dulieuchu.domain.SKhoiNganhDaoTao;
import com.fds.flex.hbs.dulieuchu.domain.TKeHoachTuyenSinh;
import com.fds.flex.hbs.dulieuchu.service.dto.SChuanChuongTrinhDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.SKhoiNganhDaoTaoDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TKeHoachTuyenSinhDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link SKhoiNganhDaoTao} and its DTO {@link SKhoiNganhDaoTaoDTO}.
 */
@Mapper(componentModel = "spring")
public interface SKhoiNganhDaoTaoMapper extends EntityMapper<SKhoiNganhDaoTaoDTO, SKhoiNganhDaoTao> {
    @Mapping(target = "sChuanChuongTrinh", source = "sChuanChuongTrinh", qualifiedByName = "sChuanChuongTrinhId")
    @Mapping(target = "tKeHoachTuyenSinh", source = "tKeHoachTuyenSinh", qualifiedByName = "tKeHoachTuyenSinhId")
    SKhoiNganhDaoTaoDTO toDto(SKhoiNganhDaoTao s);

    @Named("sChuanChuongTrinhId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    SChuanChuongTrinhDTO toDtoSChuanChuongTrinhId(SChuanChuongTrinh sChuanChuongTrinh);

    @Named("tKeHoachTuyenSinhId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TKeHoachTuyenSinhDTO toDtoTKeHoachTuyenSinhId(TKeHoachTuyenSinh tKeHoachTuyenSinh);
}

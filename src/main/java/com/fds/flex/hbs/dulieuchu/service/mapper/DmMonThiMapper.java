package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.DmChuanKyThiDanhGia;
import com.fds.flex.hbs.dulieuchu.domain.DmMonThi;
import com.fds.flex.hbs.dulieuchu.service.dto.DmChuanKyThiDanhGiaDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.DmMonThiDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link DmMonThi} and its DTO {@link DmMonThiDTO}.
 */
@Mapper(componentModel = "spring")
public interface DmMonThiMapper extends EntityMapper<DmMonThiDTO, DmMonThi> {
    @Mapping(target = "dmChuanKyThiDanhGia", source = "dmChuanKyThiDanhGia", qualifiedByName = "dmChuanKyThiDanhGiaId")
    DmMonThiDTO toDto(DmMonThi s);

    @Named("dmChuanKyThiDanhGiaId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    DmChuanKyThiDanhGiaDTO toDtoDmChuanKyThiDanhGiaId(DmChuanKyThiDanhGia dmChuanKyThiDanhGia);
}

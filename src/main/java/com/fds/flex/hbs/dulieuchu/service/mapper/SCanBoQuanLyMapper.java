package com.fds.flex.hbs.dulieuchu.service.mapper;

import com.fds.flex.hbs.dulieuchu.domain.SCanBoQuanLy;
import com.fds.flex.hbs.dulieuchu.domain.TCoSoGiaoDuc;
import com.fds.flex.hbs.dulieuchu.service.dto.SCanBoQuanLyDTO;
import com.fds.flex.hbs.dulieuchu.service.dto.TCoSoGiaoDucDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link SCanBoQuanLy} and its DTO {@link SCanBoQuanLyDTO}.
 */
@Mapper(componentModel = "spring")
public interface SCanBoQuanLyMapper extends EntityMapper<SCanBoQuanLyDTO, SCanBoQuanLy> {
    @Mapping(target = "tCoSoGiaoDuc", source = "tCoSoGiaoDuc", qualifiedByName = "tCoSoGiaoDucId")
    SCanBoQuanLyDTO toDto(SCanBoQuanLy s);

    @Named("tCoSoGiaoDucId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TCoSoGiaoDucDTO toDtoTCoSoGiaoDucId(TCoSoGiaoDuc tCoSoGiaoDuc);
}
